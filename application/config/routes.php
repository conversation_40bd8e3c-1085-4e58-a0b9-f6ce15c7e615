<?php if (!defined('BASEPATH')) exit('No direct script access allowed');
/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	http://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There area two reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router what URI segments to use if those provided
| in the URL cannot be matched to a valid route.
|
*/

$route['default_controller']                             = "inicial";
$route['404_override']                                   = '';
$route['admin/imoveis/page/(\d+)']                       = "admin/imovel/index/$3";
$route['admin/imoveis/search/page/(\d+)']                = "admin/imovel/search/$3";
$route['admin/([a-z]+)/page/(\d+)']                      = "admin/$1/index/$3";
$route['admin/([a-z]+)/page']                            = "admin/$1/index/1";
$route['admin/imoveis']                                  = "admin/imovel";
$route['admin/imovel/form2']                            = "admin/imovel/form";
$route['admin/imovel/form2/(\d+)']                       = "admin/imovel/form/$1";
$route['admin/composicaoimovel/(\d+)']                   = "admin/composicaoimovel/index/$1";
$route['admin/pacoteimovel/(\d+)']                       = "admin/pacoteimovel/index/$1";
$route['admin/fasedaobraimovel/(\d+)']                   = "admin/fasedaobraimovel/index/$1";
$route['admin/infraestruturaimovel/(\d+)']               = "admin/infraestruturaimovel/index/$1";
$route['admin/categoriaimovel/(\d+)']                    = "admin/categoriaimovel/index/$1";
$route['admin/fotoimovel/(\d+)']                         = "admin/fotoimovel/index/$1";
$route['admin/arquivoimovel/(\d+)']                      = "admin/arquivoimovel/index/$1";
$route['admin/tipos_sem_imovel/(:any)']                  = "admin/tipo/tipos_sem_imoveis/$1";
$route['admin/tipos_sem_imovel/(:any)/(:any)']           = "admin/tipo/tipos_sem_imoveis/$1/$2";
$route['admin/bairros_sem_imoveis/(:any)']               = "admin/bairro/bairros_sem_imoveis/$1";
$route['admin/bairros_sem_imoveis/(:any)/(:any)']        = "admin/bairro/bairros_sem_imoveis/$1/$2";
$route['admin/bairros_sem_imoveis/(:any)/(:any)/(:any)'] = "admin/bairro/bairros_sem_imoveis/$1/$2/$3";
$route['admin/imovel/fichacompleta/(:any)/(\d+)']        = "admin/imovel/fichacompleta/$1/$2";
$route['admin/relatorios/visitas']                       = "admin/relatorio/visitas";
$route['admin/relatorios/(:any)']                        = "admin/relatorio/index/$1";
$route['admin/atendimento/json/(:any)']                  = "admin/atendimento/json/$1";
$route['admin/tiposexportacao/salvar']                   = "admin/tipoexportacao/salvar";
$route['admin/tipoexportacao/(:any)']                    = "admin/tipoexportacao/index/$1";
$route['admin/composicoesexportacao/salvar']             = "admin/composicaoexportacao/salvar";
$route['admin/composicaoexportacao/(:any)']              = "admin/composicaoexportacao/index/$1";
$route['admin/historico']                                = "admin/agendamento/buscar";
$route['admin/avisos']                                   = "admin/aviso";
$route['admin/faq/(\d+)']                                = "admin/faq/detalhes/$1";
$route['admin/proprietario/form2']                       = "admin/proprietario/form";
$route['admin/proprietario/form2/(\d+)']                 = "admin/proprietario/form/$1";
$route['admin/etiqueta']                                 = "admin/etiquetadeatendimento/index";
$route['admin/etiqueta/cadastrar']                       = "admin/etiquetadeatendimento/cadastrar";
$route['admin/etiqueta/search']                          = "admin/etiquetadeatendimento/search";
$route['admin/etiquetasdeatendimento/deletar/(\d+)']     = "admin/etiquetadeatendimento/deletar/$1";
$route['admin/etiqueta/json']                            = "admin/etiquetadeatendimento/json";
$route['admin/etiqueta/cadastrar_funil']                  = "admin/etiquetadeatendimento/cadastrar_funil";

$route['tipos/(:any)/(:any)'] = "tipo/cidades/$1/$2";
$route['tipos/(:any)']        = "tipo/index/$1";

$route['imoveis/buscar/(:any)']        = "imovel/buscar/$1";
$route['imoveis/mapa/(:any)']          = "imovel/mapa/$1";
$route['imoveis/favoritar/(:any)']     = "imovel/favoritar/$1";
$route['imoveis/(:any)/(:any)/(:any)'] = "imovel/index/$1/$2/$3";
$route['imoveis/(:any)/(:any)']        = "imovel/index/$1/$2";
$route['imoveis/buscar']               = "imovel/buscar";
$route['imoveis/favoritos']            = "imovel/favoritos";
$route['imoveis/trocar_modo']          = "imovel/trocar_modo";

$route['imoveis/(:any)']               = "imovel/index/$1";

$route['imovel/pdf/(\d+)']             = "imovel/pdf/$1";
$route['imovel/(:any)/(\d+)']          = "imovel/detalhes/$1/$2";
$route['imovel/(:any)']                = "imovel/detalhes/$2";

$route['saiba-mais']  = "saibamais";
$route['email']       = "contato/email";
$route['anuncie']     = "contato/anuncie";
$route['procuramos']  = "contato/procuramos";
$route['ligar']       = "contato/ligar";
$route['mapa/(:any)'] = "imovel/mapa/$1";
$route['politica-de-privacidade']  = "saibamais/privacidade";

$route['exportar/proprietarios/(:any)'] = "exportar/proprietarios/$1";
$route['exportar/clientes/(:any)'] = "exportar/clientes/$1";
$route['exportar/usuarios/(:any)'] = "exportar/usuarios/$1";
$route['exportar/cards/(:any)'] = "exportar/cards/$1";
$route['exportar/gerar_chave/(\d+)'] = "exportar/gerar_chave/$1";
$route['exportar/colibra/(:any)']    = "exportar/colibra/$1";
$route['exportar/(:any)/(:any)']     = "exportar/exportacao/$1/$2";
$route['exportar/v20']               = "exportar/axis";
$route['exportar/v20/316b44139a9742cfca5f9c640c47f008f6b73c1b'] = "exportar/axis";
$route['emailmarketing/(:any)']      = "emailmarketing/index/$1";
$route['deletar_fotos']              = "migracao/deletar";
$route['admin/cadastronovo']         = "admin/adesaozero/naofinalizados";

$route['arquivoimovel/(\d+)/(:any)'] = "arquivoimovel/acessar/$1/$2";

$route['api2/tipos'] = "api-mobile/tipo/tipos";
$route['api2/cadastrar_imovel'] = "api-mobile/imovel/cadastrar";
$route['api2/imoveis'] = "api-mobile/imovel/imoveis";
$route['api2/imovel'] = "api-mobile/imovel/detalhes";
$route['api2/cadastrar_foto_imovel/(\d+)'] = "api-mobile/imovel/uploadfoto/$1";
$route['api2/cadastrar_comodo'] = "api-mobile/comodo/cadastrar";
$route['api2/editar_descricao_foto'] = "api-mobile/comodo/editar";

$route['api2/clientes'] = "api-mobile/cliente/listar";
$route['api2/cliente'] = "api-mobile/cliente/detalhes";
$route['api2/cliente/agendamentos'] = "api-mobile/cliente/agendamentos";
$route['api2/cadastrar_agendamento'] = "api-mobile/cliente/cadastrar_agendamento";
$route['api2/cadastrar_recontato'] = "api-mobile/cliente/cadastrar_recontato";
$route['api2/ofertar_imovel'] = "api-mobile/cliente/ofertar_imovel";
$route['api2/cadastrar_cliente'] = "api-mobile/cliente/cadastrar";

$route['api2/usuarios'] = "api-mobile/usuario/listar";
$route['api2/cadastrar_usuario'] = "api-mobile/usuario/cadastrar";
$route['api2/deletar_usuario/(\d+)'] = "api-mobile/usuario/deletar/$1";

$route['api2/proprietarios'] = "api-mobile/proprietario/listar";
$route['api2/proprietario'] = "api-mobile/proprietario/detalhes";
$route['api2/cadastrar_proprietario'] = "api-mobile/proprietario/cadastrar";
$route['api2/deletar_proprietario/(\d+)'] = "api-mobile/proprietario/deletar/$1";

$route['api2/composicoes'] = "api-mobile/composicao/listar";
$route['api2/cadastrar_composicao'] = "api-mobile/composicao/cadastrar";
$route['api2/cadastrar_composicao_imovel'] = "api-mobile/composicao/cadastrar_para_imovel";
$route['api2/deletar_composicao_imovel/(\d+)'] = "api-mobile/composicao/deletar_composicao_imovel/$1";
$route['api2/editar_composicao_imovel'] = "api-mobile/composicao/editar_composicao_imovel";
$route['api2/ordenar_composicoes'] = "api-mobile/composicao/ordenar";

$route['api2/infraestruturas'] = "api-mobile/infraestrutura/listar";
$route['api2/cadastrar_infraestrutura'] = "api-mobile/infraestrutura/cadastrar";
$route['api2/cadastrar_infraestrutura_imovel'] = "api-mobile/infraestrutura/cadastrar_para_imovel";
$route['api2/deletar_infraestrutura_imovel/(\d+)'] = "api-mobile/infraestrutura/deletar_infraestrutura_imovel/$1";
$route['api2/ordenar_infraestruturas'] = "api-mobile/infraestrutura/ordenar";

$route['api2/contatos'] = "api-mobile/contato/listar";
$route['api2/cadastrar_contato'] = "api-mobile/contato/cadastrar";

$route['api2/visitas'] = "api-mobile/visita/listar";
$route['api2/cadastrar_visita'] = "api-mobile/visita/cadastrar";
$route['api2/deletar_visita/(\d+)'] = "api-mobile/visita/deletar/$1";

$route['api2/funil/leads'] = "api-mobile/funil/leads";
$route['api2/funil/tipos'] = "api-mobile/funil/tipos";
$route['api2/funil/status'] = "api-mobile/funil/status";
$route['api2/funil/etapas'] = "api-mobile/funil/etapas";
$route['api2/funil/detalhes'] = "api-mobile/funil/detalhes";
$route['api2/funil/log_status'] = "api-mobile/funil/log_status";
$route['api2/funil/log_status_salvar'] = "api-mobile/funil/log_status_salvar";
$route['api2/funil/arquivos'] = "api-mobile/funil/arquivos";
$route['api2/funil/log'] = "api-mobile/funil/log";
$route['api2/funil/agendamentos'] = "api-mobile/funil/agendamentos";
$route['api2/funil/salvar'] = "api-mobile/funil/salvar";
$route['api2/funil/upload-arquivo'] = "api-mobile/funil/uploadarquivo";
$route['api2/etiquetas'] = "api-mobile/funil/etiquetas";
$route['api2/funil/cadastrar_etiqueta'] = "api-mobile/funil/cadastrar_etiqueta";

$route['api2/atendimento'] = "api-mobile/atendimento/detalhes";
$route['api2/atendimento/imoveis_perfil'] = "api-mobile/atendimento/imoveis_perfil";
$route['api2/atendimento/salvar'] = "api-mobile/atendimento/salvar";

$route['pedidos/descadastrar/(:any)'] = "descadastrar/pedido/$1";

$route['leads/(\d+)'] = "leads/index/$1";

$route['portal/(:any)/exportar/(:any)'] = "portal/exportar/$1/$2";

$route['admin/download/(:any)'] = "admin/download/index/$1";

$route['cdn/(\d+)/(\d+)/(:any)'] = "foto/cdn/$1/$2/$3";
$route['teste_cdn/(:any)'] = "foto/teste_cdn/$1";


/* End of file routes.php */
/* Location: ./application/config/routes.php */