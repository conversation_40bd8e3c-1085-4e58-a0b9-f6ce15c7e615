<?php header('Content-type: text/xml charset=utf-8', true); ?>
<?= "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" ?>

<?php
function simNao($val)
{
    return ($val == '1') ? 'Sim' : 'Não';
}
?>

<Usuarios>
    <DataHora><?= $data_hora ?></DataHora>
    <?php foreach ($usuarios as $usuario) { ?>
        <Usuario>
            <ID><?= isset($usuario['id']) ? $usuario['id'] : '' ?></ID>
            <Nome><![CDATA[<?= isset($usuario['nome']) ? $usuario['nome'] : '' ?>]]></Nome>
            <Login><![CDATA[<?= isset($usuario['login']) ? $usuario['login'] : '' ?>]]></Login>
            <Apelido><![CDATA[<?= isset($usuario['apelido']) ? $usuario['apelido'] : '' ?>]]></Apelido>
            <Email><?= isset($usuario['email']) ? $usuario['email'] : '' ?></Email>
            <Ativo><?= simNao(isset($usuario['ativo']) ? $usuario['ativo'] : '0') ?></Ativo>
            <Nivel>
                <ID><?= isset($usuario['nivel']) ? $usuario['nivel'] : '' ?></ID>
                <Descricao><![CDATA[<?= isset($usuario['nivel_extenso']) ? $usuario['nivel_extenso'] : '' ?>]]></Descricao>
            </Nivel>
            <Contatos>
                <Telefone><?= isset($usuario['telefone']) ? $usuario['telefone'] : '' ?></Telefone>
                <Celular><?= isset($usuario['celular']) ? $usuario['celular'] : '' ?></Celular>
                <WhatsApp><?= isset($usuario['whatsapp']) ? $usuario['whatsapp'] : '' ?></WhatsApp>
            </Contatos>
            <Endereco>
                <Logradouro><![CDATA[<?= isset($usuario['endereco']) ? $usuario['endereco'] : '' ?>]]></Logradouro>
                <CEP><?= isset($usuario['cep']) ? $usuario['cep'] : '' ?></CEP>
                <Cidade><![CDATA[<?= isset($usuario['cidade']) ? $usuario['cidade'] : '' ?>]]></Cidade>
            </Endereco>
            <Profissional>
                <CRECI><?= isset($usuario['creci_numero']) ? $usuario['creci_numero'] : '' ?></CRECI>
                <Cargo><![CDATA[<?= isset($usuario['cargo']) ? $usuario['cargo'] : '' ?>]]></Cargo>
                <Equipe><![CDATA[<?= isset($usuario['equipe']) ? $usuario['equipe'] : '' ?>]]></Equipe>
                <GerenteID><?= isset($usuario['gerente_id']) ? $usuario['gerente_id'] : '' ?></GerenteID>
                <Gerente><![CDATA[<?= isset($usuario['gerente']) ? $usuario['gerente'] : '' ?>]]></Gerente>
            </Profissional>
            <Configuracoes>
                <CRM><?= simNao(isset($usuario['crm']) ? $usuario['crm'] : '0') ?></CRM>
                <RepasseContato><?= simNao(isset($usuario['repasse_contato']) ? $usuario['repasse_contato'] : '0') ?></RepasseContato>
                <OrdemLead><?= isset($usuario['ordem_lead']) ? $usuario['ordem_lead'] : '' ?></OrdemLead>
            </Configuracoes>
            <DataCadastro><?= isset($usuario['inclusao']) ? $usuario['inclusao'] : '' ?></DataCadastro>
            <UltimaAlteracao><?= isset($usuario['alteracao']) ? $usuario['alteracao'] : '' ?></UltimaAlteracao>
        </Usuario>
    <?php } ?>
</Usuarios>